#!/usr/bin/env python
"""
* @author: cz
* @description: 类型转换系统基础接口定义
"""

from abc import ABC, abstractmethod
from typing import Any, Generic, Optional, TypeVar

from miniboot.errors import ConversionError


T = TypeVar("T")


class Converter(ABC, Generic[T]):
    """类型转换器接口

    定义从源类型到目标类型的转换逻辑.
    """

    @abstractmethod
    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以进行转换

        Args:
            source_type: 源类型
            target_type: 目标类型

        Returns:
            如果可以转换返回 True,否则返回 False
        """
        pass

    @abstractmethod
    def convert(self, source: Any, target_type: type[T]) -> T:
        """执行类型转换

        Args:
            source: 源值
            target_type: 目标类型

        Returns:
            转换后的值

        Raises:
            ConversionError: 转换失败时抛出
        """
        pass


class ConversionRegistry(ABC):
    """转换注册器接口

    注册和管理多个转换器,提供统一的转换入口.
    """

    @abstractmethod
    def can_convert(self, source_type: type, target_type: type) -> bool:
        """检查是否可以进行转换

        Args:
            source_type: 源类型
            target_type: 目标类型

        Returns:
            如果可以转换返回 True,否则返回 False
        """
        pass

    @abstractmethod
    def convert(self, source: Any, target_type: type[T]) -> T:
        """执行类型转换

        Args:
            source: 源值
            target_type: 目标类型

        Returns:
            转换后的值

        Raises:
            ConversionError: 转换失败时抛出
        """
        pass

    @abstractmethod
    def add_converter(self, converter: Converter) -> None:
        """添加转换器

        Args:
            converter: 转换器实例
        """
        pass

    def can_convert_chain(self, source_type: type, target_type: type, max_depth: int = 3) -> bool:
        """检查是否可以通过链式转换进行转换

        Args:
            source_type: 源类型
            target_type: 目标类型
            max_depth: 最大转换深度

        Returns:
            如果可以通过链式转换返回 True,否则返回 False
        """
        return self._find_conversion_path(source_type, target_type, max_depth) is not None

    def convert_chain(self, source: Any, target_type: type[T], max_depth: int = 3) -> T:
        """执行链式类型转换

        Args:
            source: 源值
            target_type: 目标类型
            max_depth: 最大转换深度

        Returns:
            转换后的值

        Raises:
            ConversionError: 转换失败时抛出
        """
        source_type = type(source)
        conversion_path = self._find_conversion_path(source_type, target_type, max_depth)

        if conversion_path is None:
            raise ConversionError(f"No conversion path found from {source_type.__name__} to {target_type.__name__}", source_type, target_type)

        # 执行链式转换
        current_value = source
        for step_target_type in conversion_path:
            current_value = self.convert(current_value, step_target_type)

        return current_value

    @abstractmethod
    def _find_conversion_path(self, source_type: type, target_type: type, max_depth: int) -> Optional[list[type]]:
        """查找转换路径

        Args:
            source_type: 源类型
            target_type: 目标类型
            max_depth: 最大搜索深度

        Returns:
            转换路径列表,如果找不到返回 None
        """
        pass
