"""Mini-Boot 环境配置模块

提供环境变量管理、配置文件加载、属性解析和类型转换等功能.
"""

from .bind import Binder, BindingResult, config_properties
from .config import ConfigurationLoader
from .convert import ConversionRegistry, Converter, DefaultConversionRegistry
from .environment import (ConfigurableEnvironment, Environment,
                          StandardEnvironment)
from .merger import ConfigurationMerger
from .priority import (ConfigurationLayer, ConfigurationMergeStrategy,
                       ConfigurationPriority, ConfigurationPriorityManager,
                       ConfigurationSearchPath, get_priority)
from .resolver import PropertyResolver
from .resource import (JsonPropertySourceLoader,
                       PropertiesPropertySourceLoader, PropertySourceLoader,
                       ResourceLoader, YamlPropertySourceLoader)
from .sources import (CommandLinePropertySource, MapPropertySource,
                      MutablePropertySources, PropertySource,
                      SystemEnvironmentPropertySource)

__all__ = [
    "PropertyResolver",
    "Environment",
    "ConfigurableEnvironment",
    "StandardEnvironment",
    "PropertySource",
    "MapPropertySource",
    "MutablePropertySources",
    "CommandLinePropertySource",
    "SystemEnvironmentPropertySource",
    "Converter",
    "ConversionRegistry",
    "DefaultConversionRegistry",
    "Binder",
    "BindingResult",
    "config_properties",
    "ResourceLoader",
    "PropertySourceLoader",
    "YamlPropertySourceLoader",
    "JsonPropertySourceLoader",
    "PropertiesPropertySourceLoader",
    "ConfigurationLoader",
    "ConfigurationMerger",
    "ConfigurationPriority",
    "ConfigurationLayer",
    "ConfigurationMergeStrategy",
    "ConfigurationPriorityManager",
    "ConfigurationSearchPath",
    "get_priority",
]

__version__ = "1.0.0"
