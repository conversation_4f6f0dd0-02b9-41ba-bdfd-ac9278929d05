"""类型转换模块

提供灵活的类型转换机制,支持自定义转换器注册和链式转换.
"""

from miniboot.errors import ConversionError
from .base import ConversionRegistry, Converter
from .duration import (StrToDurationConverter, StrToFloatCompositeConverter,
                       StrToPercentageConverter, StrToSizeConverter)
from .registry import DefaultConversionRegistry
from .strings import (FloatToStringConverter, IntToFloatConverter,
                      ObjectToStringConverter, StrToBoolConverter, StrToFloatConverter,
                      StrToIntConverter, StrToListConverter)
from .types import TypeDescriptor

__all__ = [
    # 核心接口
    "Converter", "ConversionRegistry", "ConversionError", "DefaultConversionRegistry", "TypeDescriptor",
    # 字符串转换器
    "StrToBoolConverter", "StrToIntConverter", "StrToFloatConverter", "StrToListConverter",
    "IntToFloatConverter", "FloatToStringConverter", "ObjectToStringConverter",
    # 时间间隔和大小转换器
    "StrToDurationConverter", "StrToSizeConverter", "StrToPercentageConverter", "StrToFloatCompositeConverter"
]
